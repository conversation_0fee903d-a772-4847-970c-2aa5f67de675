<?php

namespace App\Exports;

use App\Models\Address;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class InvoicingAddressExport implements FromQuery, WithHeadings, WithMapping
{
    protected $address;

    public function __construct(Address $address)
    {
        $this->address = $address;
    }
    
    public function query()
    {
        // Return a query that contains only this address
        return Address::query()->where('id', $this->address->id);
    }

    public function headings(): array
    {
        return [
            // TODO: Add actual column headers here
            'ID',
            'NAME',
            'COMPANY',
            'VAT_NUMBER',
            'FISCAL_CODE',
            'SDI_CODE',
            'STREET',
            'CITY',
            'STATE',
            'ZIP',
            'COUNTRY',
        ];
    }

    public function map($address): array
    {
        return [
            // TODO: Add actual data mapping here
            $address->id,
            $address->name,
            $address->company,
            $address->vat_number,
            $address->fiscal_code,
            $address->sdi_code,
            $address->street,
            $address->city,
            $address->state,
            $address->zip,
            $address->country?->value,
        ];
    }
}
