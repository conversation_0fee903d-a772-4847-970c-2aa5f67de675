<?php

namespace App\Exports;

use App\Models\Order\Order;
use App\Models\Order\OrderRow;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithHeadings;

class OrderRowsForERPExport implements FromQuery, WithHeadings, WithMapping
{
    protected $order;

    public function __construct(Order $order)
    {
        $this->order = $order;
    }
    
    public function query()
    {
        // Get cart group IDs associated with this order
        $cartGroupIds = $this->order->cartGroups()->pluck('id')->toArray();
        
        // Filter order rows by these cart group IDs
        return OrderRow::query()->whereIn('cart_group_id', $cartGroupIds)->orderBy('sort');
    }

    public function headings(): array
    {
        return [
            // TODO: Add actual column headers here
            'ORDER_ID',
            'SKU',
            'DESCRIPTION',
            'QUANTITY',
            'SELLING_PRICE',
            'DISCOUNT',
            'PURCHASING_PRICE',
            'POSITION_ID',
        ];
    }

    public function map($orderRow): array
    {
        return [
            // TODO: Add actual data mapping here
            $this->order->id,
            $orderRow->sku,
            $orderRow->description,
            $orderRow->quantity,
            $orderRow->selling_price,
            $orderRow->discount,
            $orderRow->purchasing_price,
            $orderRow->position_id,
        ];
    }
}
