<?php

namespace App\Livewire\Pages\Orders;

use Flux\Flux;
use App\Models\User;
use App\Models\Client;
use App\Enums\VatTypes;
use App\Models\Address;
use App\Models\Partner;
use Livewire\Component;
use App\Enums\AddressTypes;
use App\Models\Order\Order;
use App\Models\PaymentTerm;
use App\Enums\OrderStatuses;
use App\Enums\ExportSources;
use App\Jobs\Export\ExportOrderFilesJob;
use App\Livewire\Forms\OrderForm;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use PrinsFrank\Standards\Country\CountryAlpha3;

class Show extends Component
{
    public Order $order;
    public OrderForm $form;
    public ?Address $invoicingAddress;
    public ?Address $shippingAddress;

    public object $callout;

    public function mount(Order $order)
    {
        $this->callout = (object) [
            'show' => false,
            'variant' => 'info',
            'icon' => 'information-circle',
            'heading' => 'Here a message.'
        ];

        $this->form->setOrder($order);

        // Load the addresses
        if ($order->status === OrderStatuses::Open) {
            $this->invoicingAddress = $order->invoicingAddress;
            $this->shippingAddress = $order->shippingAddress;
        }
        else {
            $this->invoicingAddress = $order->addresses()->where('type', AddressTypes::Invoicing->value)->first();
            $this->shippingAddress = $order->addresses()->where('type', AddressTypes::Shipping->value)->first();
        }
    }

    public function render()
    {
        $client = Client::find($this->form->client_id);

        // Set the callout message if order contains soft deleted products
        if ($this->order->orderRows()->whereHas('product', function ($query) {
            $query->onlyTrashed();
        })->count() > 0 && $this->order->status->value === OrderStatuses::Open->value) {
            $this->callout = (object) [
                'show' => true,
                'variant' => 'warning',
                'icon' => 'exclamation-circle',
                'heading' => 'This order contains OUT OF STOCK products. Please replace them!'
            ];
        }

        return view('livewire.pages.orders.show', [
            'internalReferents' => User::query()->where('is_employee', TRUE)->orderBy('id', 'desc')->get(),
            'areaManagers' => User::query()->where('is_employee', TRUE)->orderBy('id', 'desc')->get(),
            'clients' => Client::orderBy('id', 'desc')->get(),
            'partners' => $this->form->client_id 
                ? Partner::where('id', $client?->partner_id)->get()
                : [],
            'invoicingAddresses' => $this->form->client_id 
                ? Address::where('addressable_id', $this->form->client_id)
                    ->where('addressable_type', Client::class)
                    ->where('type', AddressTypes::Invoicing->value)
                    ->orderBy('id', 'desc')
                    ->get()
                : [],
            'shippingAddresses' => $this->form->client_id 
                ? Address::where('addressable_id', $this->form->client_id)
                    ->where('addressable_type', Client::class)
                    ->where('type', AddressTypes::Shipping->value)
                    ->orderBy('id', 'desc')
                    ->get()
                : [],
            'paymentTerms' => PaymentTerm::orderBy('id', 'desc')->get(),
            'vatTypes' => VatTypes::cases(),
            'projects' => $this->form->client_id 
                ? $client?->projects
                : [],
            'addressTypes' => AddressTypes::cases(),
            'countries' => CountryAlpha3::cases(),
        ]);
    }

    public function edit($id): void
    {
        $this->redirectRoute('orders.edit', ['order' => $id], navigate: true);
    }

    public function delete($id): void
    {
        Order::findOrFail($id)->delete();
        
        Flux::toast(
            variant: 'success',
            text: 'The ORDER has been deleted.'
        );

        $this->redirectRoute('orders.index', navigate: true);
    }

    public function downloadConfirmation()
    {
        if (!Storage::disk(config('filesystems.private'))->exists($this->form->confirmation_file ?? ' ')) {

            Flux::toast(
                variant: 'danger',
                text: 'The FILE does not exist.'
            );

            return false;
        }

        $url = Storage::disk(config('filesystems.private'))
            ->temporaryUrl($this->form->confirmation_file, now()->addMinutes(5));

        return response()->streamDownload(function () use ($url) {
            echo file_get_contents($url);
        }, basename($this->form->confirmation_file));
    }

    public function openCreator($id): void
    {
        $this->redirectRoute('orders.creator', ['order' => $id], navigate: true);
    }

    public function submit()
    {
        DB::beginTransaction();

        // Check if the order has rows
        if ($this->order->orderRows()->count() === 0) {
            Flux::toast(
                variant: 'danger',
                text: 'The ORDER has no rows.'
            );

            DB::rollBack();
            
            return false;
        }

        // Check if the order has custom products
        if ($this->order->orderRows()->where('custom_product_id', '!=', null)->count() > 0) {
            Flux::toast(
                variant: 'danger',
                text: 'The ORDER has custom products.'
            );

            DB::rollBack();
            
            return false;
        }

        // Check if the order has addresses
        if ($this->order->invoicing_address_id === null || $this->order->shipping_address_id === null) {
            Flux::toast(
                variant: 'danger',
                text: 'The ORDER has no addresses.'
            );

            DB::rollBack();
            
            return false;
        }

        // Transfer the product data to the order row
        $this->order->orderRows()->each(function ($row) {
            $row->update([
                'sku' => $row->product->sku,
                'description' => $row->product->description,
                'purchasing_price' => $row->product->purchasing_price,
                'selling_price' => $row->product->selling_price,
                'discount' => $row->product->discount,
            ]);
        });

        // Transfer the addresses data to the order using addresses table
        $this->order->addresses()->saveMany([
            $this->invoicingAddress,
            $this->shippingAddress,
        ]);

        // Update the order status
        $this->order->update([
            'status' => OrderStatuses::Submitted->value,
        ]);

        DB::commit();

        // Dispatch job to create xlsx file for invoicing address, shipping address and order (to be imported in the ERP)
        // Note: Dispatched AFTER commit to ensure data consistency
        ExportOrderFilesJob::dispatch(
            $this->order,
            ExportSources::OrderSubmit,
            Auth::id()
        )->onQueue('default');

        Flux::toast(
            variant: 'success',
            text: 'The ORDER has been submitted.'
        );
    }

    public function showInvoicingAddress()
    {
        Flux::modal('view-invoicing-address')->show();
    }

    public function showShippingAddress()
    {
        Flux::modal('view-shipping-address')->show();
    }
}
