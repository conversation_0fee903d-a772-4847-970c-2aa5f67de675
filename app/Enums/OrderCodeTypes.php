<?php

namespace App\Enums;

enum OrderCodeTypes: string
{
    case Retail = 'retail';
    case Trade = 'trade';

    public function label()
    {
        return match ($this) {
            static::Retail => 'Retail',
            static::Trade => 'Trade',
        };
    }

    public function code()
    {
        return match ($this) {
            static::Retail => 'RTL',
            static::Trade => 'TRD',
        };
    }
}
