<?php

namespace App\Enums;

enum CommercialCategories: string
{
    case Store = 'store';
    case VirtualStore = 'virtual_store';
    case FurnitureStore = 'furniture_store';
    case InteriorDesigner = 'interior_designer';
    case ArchitectureStudio = 'architecture_studio';
    case GeneralContractor = 'general_contractor';
    case Shipyard = 'shipyard';
    case Developer = 'developer';
    case PrivateClient = 'private_client';
    case Corporate = 'corporate';
    case TradingCompany = 'trading_company';
    case FranchisingBergomi = 'franchising_bergomi';
    case VIPPrivateClient = 'vip_private_client';

    public function label()
    {
        return match ($this) {
            static::Store => 'Store',
            static::VirtualStore => 'Virtual Store',
            static::FurnitureStore => 'Furniture Store',
            static::InteriorDesigner => 'Interior Designer',
            static::ArchitectureStudio => 'Architecture Studio',
            static::GeneralContractor => 'General Contractor',
            static::Shipyard => 'Shipyard',
            static::Developer => 'Developer',
            static::PrivateClient => 'Private Client',
            static::Corporate => 'Corporate',
            static::TradingCompany => 'Trading Company',
            static::FranchisingBergomi => 'Franchising Bergomi',
            static::VIPPrivateClient => 'VIP Private Client',
        };
    }
}
