<?php

namespace App\Enums;

enum VatTypes: string
{
    case Standard = 'standard';
    case NI74 = 'ni74';
    case NI41 = 'ni41';
    case NI41T = 'ni41t';
    case NI58 = 'ni58';
    case NI8IT = 'ni8it';
    case NI8LA = 'ni8la';
    case NI8LB = 'ni8lb';
    case NI8TR = 'ni8tr';

    public function label(): string
    {
        return match ($this) {
            static::Standard => '22 - IVA 22%',
            static::NI74 => 'NI.74 - Non Imp. Art.74',
            static::NI41 => 'NI41 - Non Imp. Art.41',
            static::NI41T => 'NI41T - Non Imp. Art.41 con Triangolazione',
            static::NI58 => 'NI58 - Non Imp. Art.58',
            static::NI8IT => 'NI8IT - Non Imp. Art.8 con Dich. Intento',
            static::NI8LA => 'NI8LA - Non Imp. Art.8 Lettera A',
            static::NI8LB => 'NI8LB - Non Imp. Art.8 Lettera B',
            static::NI8TR => 'NI8TR - Non Imp. Art.8 Triangolazione',
        };
    }

    public function rate(): float
    {
        return match ($this) {
            static::Standard => 0.22,
            static::NI8 => 0.0,
            static::NI41 => 0.0,
            static::NI8IT => 0.0,
        };
    }

    public function adhocCode(): string
    {
        return match ($this) {
            static::Standard => '',
            static::NI8 => 'NI8',
            static::NI41 => 'NI41',
            static::NI8IT => 'NI8IT',
        };
    }
}
