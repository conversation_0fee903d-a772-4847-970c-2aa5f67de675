<?php

namespace App\Enums;

enum VatTypes: string
{
    case Standard = 'standard';
    case NI8 = 'ni8';
    case NI41 = 'ni41';
    case NI8IT = 'ni8it';

    public function label(): string
    {
        return match ($this) {
            static::Standard => '22 - IVA 22%',
            static::NI8 => 'NI8 - Non Imp. Art.8',
            static::NI41 => 'NI41 - Non Imp. Art.41',
            static::NI8IT => 'NI8IT - Non Imp. Art.8 con Dich. Intento',
        };
    }

    public function rate(): float
    {
        return match ($this) {
            static::Standard => 0.22,
            static::NI8 => 0.0,
            static::NI41 => 0.0,
            static::NI8IT => 0.0,
        };
    }
}
